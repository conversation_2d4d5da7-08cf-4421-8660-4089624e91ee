<script>
	import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>alogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON>alogHeader, DialogTitle } from '$lib/components/ui/dialog';
	import { Input } from '$lib/components/ui/input';
	import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '$lib/components/ui/select';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { Button } from '$lib/components/ui/button';
	import { Label } from '$lib/components/ui/label';

	let { 
		open = $bindable(false), 
		nodeData = $bindable(null),
		onSave = () => {}
	} = $props();

	// Local state for editing
	let editedValue = $state(null);
	let isValid = $state(true);
	let validationMessage = $state('');

	// Watch for nodeData changes to initialize editedValue
	$effect(() => {
		if (nodeData && open) {
			// Initialize editedValue based on the setting type and current value
			const settingId = nodeData.id?.replace('-setting', '');
			const config = settingConfigs[settingId];

			if (config && config.type === 'number-with-random') {
				// Handle number-with-random types
				if (typeof nodeData.currentValue === 'object' && nodeData.currentValue.random !== undefined) {
					editedValue = structuredClone(nodeData.currentValue);
				} else {
					// Convert simple number to number-with-random structure
					editedValue = { random: false, value: nodeData.currentValue };
				}
			} else if (config && config.type === 'select-with-random') {
				// Handle select-with-random types
				if (typeof nodeData.currentValue === 'object' && nodeData.currentValue.random !== undefined) {
					editedValue = structuredClone(nodeData.currentValue);
				} else {
					// Convert simple value to select-with-random structure
					editedValue = { random: false, value: nodeData.currentValue };
				}
			} else {
				editedValue = structuredClone(nodeData.currentValue);
			}
		}
	});

	// Setting type configurations
	const settingConfigs = {
		// Mode Zone Settings
		'scoring': {
			type: 'select',
			label: 'Scoring Method',
			options: [
				{ value: 'count', label: 'Count' },
				{ value: 'hint', label: 'Hint' },
				{ value: 'speed', label: 'Speed' }
			],
			default: 'count'
		},
		'answering': {
			type: 'select',
			label: 'Answering Method',
			options: [
				{ value: 'typing', label: 'Typing' },
				{ value: 'mix', label: 'Mix' },
				{ value: 'multiple-choice', label: 'Multiple Choice' }
			],
			default: 'typing'
		},
		
		// General Zone Settings
		'players': {
			type: 'number',
			label: 'Number of Players',
			min: 1,
			max: 100,
			default: 8
		},
		'team-size': {
			type: 'number',
			label: 'Team Size',
			min: 1,
			max: 8,
			default: 1
		},
		'songs': {
			type: 'number-with-random',
			label: 'Number of Songs',
			min: 5,
			max: 100,
			default: 20,
			allowRandom: true
		},
		'watched-distribution': {
			type: 'select',
			label: 'Watched Distribution',
			options: [
				{ value: 'random', label: 'Random' },
				{ value: 'equal', label: 'Equal' }
			],
			default: 'random'
		},

		// Quiz Zone Settings
		'guess-time': {
			type: 'number-with-random',
			label: 'Guess Time (seconds)',
			min: 1,
			max: 60,
			default: 20,
			allowRandom: true
		},
		'extra-time': {
			type: 'number-with-random',
			label: 'Extra Guess Time (seconds)',
			min: 0,
			max: 15,
			default: 0,
			allowRandom: true
		},
		'sample-point': {
			type: 'range',
			label: 'Sample Point (%)',
			min: 0,
			max: 100,
			default: { start: 0, end: 100 }
		},
		'playback-speed': {
			type: 'select-with-random',
			label: 'Playback Speed',
			options: [
				{ value: 1, label: '1x' },
				{ value: 1.5, label: '1.5x' },
				{ value: 2, label: '2x' },
				{ value: 4, label: '4x' }
			],
			default: 1,
			allowRandom: true
		},
		'modifiers': {
			type: 'checkboxes',
			label: 'Modifiers',
			options: [
				{ key: 'skipGuessing', label: 'Skip Guessing', default: true },
				{ key: 'skipResults', label: 'Skip Results', default: true },
				{ key: 'queueing', label: 'Queueing', default: true }
			]
		},

		// Anime Zone Settings
		'anime-type': {
			type: 'checkboxes',
			label: 'Anime Types',
			options: [
				{ key: 'tv', label: 'TV', default: true },
				{ key: 'movie', label: 'Movie', default: true },
				{ key: 'ova', label: 'OVA', default: true },
				{ key: 'ona', label: 'ONA', default: true },
				{ key: 'special', label: 'Special', default: true }
			]
		}
	};

	// Get configuration for current node
	const config = $derived(nodeData ? settingConfigs[nodeData.id?.replace('-setting', '')] : null);

	// Validation function
	function validateValue(value, config) {
		if (!config) return { valid: true, message: '' };

		switch (config.type) {
			case 'number':
				const num = Number(value);
				if (isNaN(num) || num < config.min || num > config.max) {
					return { valid: false, message: `Value must be between ${config.min} and ${config.max}` };
				}
				break;
			case 'number-with-random':
				if (typeof value === 'object' && value.random) {
					if (value.min < config.min || value.max > config.max || value.min > value.max) {
						return { valid: false, message: `Range must be between ${config.min}-${config.max} and min ≤ max` };
					}
				} else if (typeof value === 'object' && value.value !== undefined) {
					const num = Number(value.value);
					if (isNaN(num) || num < config.min || num > config.max) {
						return { valid: false, message: `Value must be between ${config.min} and ${config.max}` };
					}
				} else {
					const num = Number(value);
					if (isNaN(num) || num < config.min || num > config.max) {
						return { valid: false, message: `Value must be between ${config.min} and ${config.max}` };
					}
				}
				break;
			case 'range':
				if (value.start < config.min || value.end > config.max || value.start > value.end) {
					return { valid: false, message: `Range must be between ${config.min}-${config.max}% and start ≤ end` };
				}
				break;
		}
		return { valid: true, message: '' };
	}

	// Update validation when editedValue changes
	$effect(() => {
		if (editedValue && config) {
			const validation = validateValue(editedValue, config);
			isValid = validation.valid;
			validationMessage = validation.message;
		}
	});

	function handleSave() {
		if (!isValid || !nodeData) return;
		
		onSave({
			nodeId: nodeData.id,
			newValue: editedValue
		});
		
		open = false;
	}

	function handleCancel() {
		open = false;
		editedValue = null;
	}
</script>

<Dialog bind:open>
	<DialogContent class="sm:max-w-md">
		<DialogHeader>
			<DialogTitle class="flex items-center gap-2">
				<span class="text-xl">{nodeData?.icon}</span>
				Edit {nodeData?.title}
			</DialogTitle>
			<DialogDescription>
				Configure the settings for this node.
			</DialogDescription>
		</DialogHeader>

		{#if config && editedValue !== null}
			<div class="space-y-4 py-4">
				{#if config.type === 'select'}
					<div class="space-y-2">
						<Label for="setting-value">{config.label}</Label>
						<Select bind:value={editedValue}>
							<SelectTrigger>
								<SelectValue placeholder="Select an option..." />
							</SelectTrigger>
							<SelectContent>
								{#each config.options as option}
									<SelectItem value={option.value}>{option.label}</SelectItem>
								{/each}
							</SelectContent>
						</Select>
					</div>

				{:else if config.type === 'number'}
					<div class="space-y-2">
						<Label for="setting-value">{config.label}</Label>
						<Input
							id="setting-value"
							type="number"
							bind:value={editedValue}
							min={config.min}
							max={config.max}
							class={!isValid ? 'border-red-500' : ''}
						/>
						{#if !isValid}
							<p class="text-sm text-red-500">{validationMessage}</p>
						{/if}
					</div>

				{:else if config.type === 'number-with-random'}
					<div class="space-y-4">
						<Label>{config.label}</Label>

						<div class="flex items-center space-x-2">
							<Checkbox
								bind:checked={editedValue.random}
								id="random-toggle"
							/>
							<Label for="random-toggle" class="text-sm">Use random range</Label>
						</div>

						{#if editedValue.random}
							<div class="grid grid-cols-2 gap-2">
								<div>
									<Label for="min-value" class="text-sm">Min</Label>
									<Input
										id="min-value"
										type="number"
										bind:value={editedValue.min}
										min={config.min}
										max={config.max}
										class={!isValid ? 'border-red-500' : ''}
									/>
								</div>
								<div>
									<Label for="max-value" class="text-sm">Max</Label>
									<Input
										id="max-value"
										type="number"
										bind:value={editedValue.max}
										min={config.min}
										max={config.max}
										class={!isValid ? 'border-red-500' : ''}
									/>
								</div>
							</div>
						{:else}
							<Input
								type="number"
								bind:value={editedValue.value}
								min={config.min}
								max={config.max}
								class={!isValid ? 'border-red-500' : ''}
							/>
						{/if}

						{#if !isValid}
							<p class="text-sm text-red-500">{validationMessage}</p>
						{/if}
					</div>

				{:else if config.type === 'range'}
					<div class="space-y-4">
						<Label>{config.label}</Label>
						<div class="grid grid-cols-2 gap-2">
							<div>
								<Label for="start-value" class="text-sm">Start (%)</Label>
								<Input
									id="start-value"
									type="number"
									bind:value={editedValue.start}
									min={config.min}
									max={config.max}
									class={!isValid ? 'border-red-500' : ''}
								/>
							</div>
							<div>
								<Label for="end-value" class="text-sm">End (%)</Label>
								<Input
									id="end-value"
									type="number"
									bind:value={editedValue.end}
									min={config.min}
									max={config.max}
									class={!isValid ? 'border-red-500' : ''}
								/>
							</div>
						</div>
						{#if !isValid}
							<p class="text-sm text-red-500">{validationMessage}</p>
						{/if}
					</div>

				{:else if config.type === 'checkboxes'}
					<div class="space-y-3">
						<Label>{config.label}</Label>
						{#each config.options as option}
							<div class="flex items-center space-x-2">
								<Checkbox
									bind:checked={editedValue[option.key]}
									id={option.key}
								/>
								<Label for={option.key} class="text-sm">{option.label}</Label>
							</div>
						{/each}
					</div>

				{:else if config.type === 'select-with-random'}
					<div class="space-y-4">
						<Label>{config.label}</Label>

						<div class="flex items-center space-x-2">
							<Checkbox
								bind:checked={editedValue.random}
								id="random-speed-toggle"
							/>
							<Label for="random-speed-toggle" class="text-sm">Use random range</Label>
						</div>

						{#if editedValue.random}
							<div class="space-y-2">
								<Label class="text-sm">Select allowed speeds:</Label>
								{#each config.options as option}
									<div class="flex items-center space-x-2">
										<Checkbox
											bind:checked={editedValue.options[option.value]}
											id={`speed-${option.value}`}
										/>
										<Label for={`speed-${option.value}`} class="text-sm">{option.label}</Label>
									</div>
								{/each}
							</div>
						{:else}
							<Select bind:value={editedValue}>
								<SelectTrigger>
									<SelectValue placeholder="Select speed..." />
								</SelectTrigger>
								<SelectContent>
									{#each config.options as option}
										<SelectItem value={option.value}>{option.label}</SelectItem>
									{/each}
								</SelectContent>
							</Select>
						{/if}
					</div>
				{/if}
			</div>
		{/if}

		<DialogFooter>
			<Button variant="outline" onclick={handleCancel}>Cancel</Button>
			<Button onclick={handleSave} disabled={!isValid}>Save Changes</Button>
		</DialogFooter>
	</DialogContent>
</Dialog>
