<script>
	import { <PERSON><PERSON>, Position } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import NodeEditDialog from './NodeEditDialog.svelte';

	let { data } = $props();

	// Extract handlers from data
	const onValueChange = data.onValueChange || (() => {});

	// Dialog state
	let dialogOpen = $state(false);
	let nodeDataForDialog = $state(null);

	// Get the color with opacity for background
	const getBackgroundColor = (color) => {
		// Convert hex to rgba with low opacity for white base
		const hex = color.replace('#', '');
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		// White background with color tint
		return `rgba(255, 255, 255, 0.95)`;
	};

	// Get the color with higher opacity for border
	const getBorderColor = (color) => {
		// Convert hex to rgba with higher opacity for brighter border
		const hex = color.replace('#', '');
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		return `rgba(${r}, ${g}, ${b}, 0.9)`;
	};

	// Get color tint for the card
	const getColorTint = (color) => {
		const hex = color.replace('#', '');
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		return `rgba(${r}, ${g}, ${b}, 0.08)`;
	};

	const backgroundColor = $derived(getBackgroundColor(data.color));
	const borderColor = $derived(getBorderColor(data.color));
	const colorTint = $derived(getColorTint(data.color));

	// Format setting value for display
	function formatSettingValue(value) {
		if (typeof value === 'object' && value !== null) {
			if (Array.isArray(value)) {
				return value.length > 0 ? `${value.length} items` : 'None';
			}

			// Handle number-with-random objects like { random: true, min: 1, max: 10 } or { random: false, value: 20 }
			if (value.random === true && value.min !== undefined && value.max !== undefined) {
				return `Random ${value.min}-${value.max}`;
			}
			if (value.random === false && value.value !== undefined) {
				return value.value.toString();
			}

			// Handle select-with-random objects
			if (value.random === true && value.options) {
				const enabledOptions = Object.keys(value.options).filter(key => value.options[key] === true);
				return enabledOptions.length > 0 ? `Random (${enabledOptions.length} options)` : 'Random (none)';
			}
			if (value.random === false && value.value !== undefined) {
				return value.value.toString();
			}

			// Handle range objects like { min: 1, max: 10 }
			if (value.min !== undefined && value.max !== undefined) {
				return `${value.min} - ${value.max}`;
			}

			// Handle percentage objects like { start: 0, end: 100 }
			if (value.start !== undefined && value.end !== undefined) {
				return `${value.start}% - ${value.end}%`;
			}

			// Handle checkbox objects like { tv: true, movie: false, ... }
			if (typeof value === 'object' && !Array.isArray(value) && value.random === undefined) {
				const enabledKeys = Object.keys(value).filter(key => value[key] === true);
				if (enabledKeys.length > 0) {
					return enabledKeys.length === Object.keys(value).length ? 'All enabled' : `${enabledKeys.length} enabled`;
				}
				return 'None enabled';
			}

			return JSON.stringify(value);
		}
		if (typeof value === 'boolean') {
			return value ? 'Enabled' : 'Disabled';
		}
		if (typeof value === 'number') {
			return value.toString();
		}
		return value || 'Not set';
	}

	// Handle node click to open edit dialog
	function handleNodeClick(event) {
		// Prevent event from bubbling to SvelteFlow
		event.stopPropagation();

		nodeDataForDialog = {
			id: data.id || data.title?.toLowerCase().replace(/\s+/g, '-'),
			title: data.title,
			icon: data.icon,
			currentValue: data.currentValue
		};

		dialogOpen = true;
	}

	// Handle save from dialog
	function handleSave(saveData) {
		// Update the node's current value
		data.currentValue = saveData.newValue;
		data.isDefault = false;

		// Call the parent's value change handler
		onValueChange({
			nodeId: saveData.nodeId,
			newValue: saveData.newValue,
			zone: data.zone
		});
	}
</script>

<div
	class="individual-setting-node w-48 relative cursor-pointer hover:shadow-md transition-shadow duration-200"
	style="background: {backgroundColor}; border: 2px solid {borderColor}; border-radius: 8px; box-shadow: 0 1px 4px rgba(0,0,0,0.1);"
	onclick={handleNodeClick}
	role="button"
	tabindex="0"
	onkeydown={(e) => e.key === 'Enter' && handleNodeClick(e)}
>
	<!-- Output handle to connect to zone area -->
	<Handle type="source" position={Position.Bottom} style="width: 10px; height: 10px; background: {data.color}; border: 2px solid white;" />

	<div class="p-2">
		<!-- Compact header -->
		<div class="flex items-center gap-2 mb-2" style="background: {colorTint}; padding: 6px; border-radius: 6px; margin: -2px; margin-bottom: 6px;">
			<span class="text-sm">{data.icon}</span>
			<div class="flex-1 min-w-0">
				<div class="text-sm font-semibold text-gray-800 truncate">{data.title}</div>
			</div>
		</div>

		<!-- Compact value display -->
		<div class="bg-gray-50 rounded p-1.5">
			<div class="text-xs font-medium text-gray-800 truncate">
				{formatSettingValue(data.currentValue)}
			</div>
		</div>
	</div>
</div>

<!-- Edit Dialog -->
<NodeEditDialog
	bind:open={dialogOpen}
	bind:nodeData={nodeDataForDialog}
	onSave={handleSave}
/>

<style>
	.individual-setting-node {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}
	
	:global(.individual-setting-node .svelte-flow__handle) {
		width: 12px;
		height: 12px;
		border: 2px solid white;
		box-shadow: 0 1px 3px rgba(0,0,0,0.1);
	}
	
	:global(.individual-setting-node .svelte-flow__handle.svelte-flow__handle-bottom) {
		bottom: -6px;
	}
</style>
